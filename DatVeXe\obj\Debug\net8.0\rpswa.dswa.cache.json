{"GlobalPropertiesHash": "G0c1JKobC3f+Aw55aQ66+R77M/vD9zXiEfTLEu3OAAo=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["VpG6RR+sGvgiG0+kRhhg3LHt7oVcu+vWUgl74iATE+s=", "Y43vTQmguR1HwfWmBThhj0laYDOF4zztgf4rar7BvqQ=", "QTwtXdWxEc1SdW/Es/sYPP3oPSgLQaReOPJ77Ly/tRA=", "QfIwLWcnRYuGHOIvSkSg1Wb7qOFV8+PyzfEaODTa9Is=", "CA5ohl5cSHV/YlScu6aWvJgsrIwv0JyA9ZtLqzAPCnI=", "MMKphBGZ00ip1cX6JVta60lUHa1dd50dTaKmUHOi128=", "1l+q4+tgl+3P3iEfii9wjmI69NnzjR9gqt4pqyy0foQ=", "Ui0bIWRsFQRARk0DqAeeISZwYkz+GtxT8T50KZbB3mQ=", "hkAMMfs4YrdvsY4Zk1Ze1sK4VE/V+t+5ogrEVF22Aq0=", "gjyUN1J6R3ADM8wd7Bc6u1GFS6HxJdMds6202HB3Ogk=", "y+gzeJcgDksGc5XokVH4sZSxhu2jQAIkIcj2x9Lw0yg=", "89qWj7NWQtq3H7Xlim/s9MT8usOSrUuQ4XNkzKMU3Lk=", "KBeIup7NZoGVAf6T2WeT4ktE7r2Ee3E5qEZg8c8rHTk=", "MgzX187z1VLS/mxCVyImLqGnZD/TGMa/MiAgMDK7SKM=", "+9FiNR2LA+JkfYRV8ZmAsfU3ZKMpnCl2m4zrQuazG64=", "YMvfR1mb73hf1tVkaUb6KMV4OtpIKH4kkzYaGuj1RHo=", "sV2tzNTaBYc5wLHykioQt+1oH1vPTm7qPJ+uZ1klSis=", "+KqglmyNEputqLd3L1J195VlOv7DcmJvdjyX9SflybE=", "I90+1tOxwORrE2lVsmuTVALRWoRsZeGU9YOcGaKDviw=", "o4lS39EKK/8+4VfqnLxI5lByKFHP3UYK/xJYrc36fuM=", "nx1OtnP+ryN4Ek2IreZ8DVVuMMizSS4IzrbsDIGSy0o=", "gnq2cLzf7cRS/AwxClZ5kg4w8kBghpRi3K5WNUCpcWY=", "ib4p/+ug1o5sTvRDO/HzRwp5ByUFUS7vlm0LNQxN/hU=", "9wrCectbVHkIl4Lgkgp7rVeQBwF5EJ/aLB6vTaaceBA=", "t9ZjYiWa6JGtmRVepu/3f6IOfX1uxaTwSCkkpreo5X0=", "efV24TdfKFTtyEE8NDmE9ok/OYHcLSGp2BlQfctC3Rg=", "BBUaI5XSk7xmMvJ7D2HwMMB5WTSh1JCDfUDfkxJlNnY=", "VyBWR+TtxknGk+IFAT9cDaVwWZ0ppOBSv2ZBwYWBaik=", "mkBzPkF6EH0WD+7gi2yQZcy9OqVuViCvgcHrsHE9TWY=", "2B9nbnM6uiIxDWI0yJJ0dIajVe+XOiMzLGMc+FzSOTI=", "Pk5XDsnw2xaA9fExKY+wAISrPS9nVrGHxRe+3FzuN7M=", "CN0DTUjOh3yiF7kschJN+mdW9fKjItQjWJwA0CPEkzA=", "USyi7wZIHuUlgdq53WPCLHJc6Yhm5bGv1CFPlcbceO4=", "cAYvqfVuLfykfBx3IVzioJZ7jXJ4+arFxDueq8z64QU=", "OZFyjoNCTUYWToVaSVMT4y+Rej8F+2OrdopRZl8syN8=", "gfisdeWrEAbsvOUbqblCrF4CANRN2oiO6TVKzy87FJk=", "k77gHl7GxJyWrGsHV9RA0xUhrS10ENDWGcxEOXtSNxw=", "JQFzeZVPMJbSWJc+343QOI3bM4Veuc7DuVOqABxT1Tk=", "ibt5UaOvNIHqDZkWaJ8TQOZfVmbeyxfuTX2aozc01cI=", "nnKSaTXJhZ5ZFfNeOUTvBiBklSxSU5U5grEGMudb08I=", "dQKlBmu5b79wLU5Vzd0yVbe+UaMfi0+w8yNFfSCsYvE=", "dBNId30ehqLvo+ls6xlvXEsJr/1Cx/YJ0r7XHVgcSIY=", "6cs72PyUDRnUL468tapRqmBbU/D+bKqhZzWuopZ3lUI=", "xYnhQ7ff5ivYR3vSNxtiXTnMI+hGBM9zNoppnpQyJrw=", "XNkuZ+F+r3OIloeF9g5uIGCOqfm3JJZpudcRBgLN8lc=", "hDm8crFxOx7iLbY3wt0y9CsTM/C3sDexH9Ijx5IC1t4=", "VQtCbFEQmI4ew8P/VkX96NMlOdqthGFQEBNHdlptDnY=", "30goXUtPmCesDGG+rYo6o025UTIXENQBoJgI81KkeR4=", "auWJKhmtHzWg2iGzseZzO/Acz2OIXevr7wQnYeXISZM=", "CWdTQHWROlTgPC6fz6dax8vVIaHTL/So3GpvCPslJ3o=", "JTUdtBo36Hg7vBf3C3G70VxptumCxsRx0fQ2c5gcE9A=", "1eVHByR4MFjlq4xjkNHvphoGS57cDQ7HlRFXQSVpQ/Q=", "C3BVvGfF0GRnZ/BSQejVI+PQnai0Xtr1zB7aFzE78lQ=", "1ekKo24BxjOqsYN27jlfCbYxpqD63RpC6RNq37911rg=", "Bt7f8zJNFpI29lcu2SSs8AXtq0ZeCbPK1iZgQZSbNSo=", "iCI8stvzQti2bnLC3WwhG5cn68QD0slw2jOUNF82n7Y=", "75BC1UKp3NCXutLEidMNgLD+rqobELklAje/bypdp2E=", "d3+fQdRgDAGnBaLL2ipbs+py8GlN7tRrugn4kyDeMnw=", "9/Ky1iY1mycg/0dq5vqoxtQzNeQrBnNKoVn6P3K59e0=", "ztOjQQpLZ2ee60e7sVwJ2fN3raRTKReQQenlOTaExBA=", "jHLDHuaOK/YZNBq9/DMDAnzZPiXu9OOBoSO4vddy/V8=", "+QC6P+K8cYhUAdKaP+R04FX5vSdlL7ncApUYYCRNOGQ=", "wA61BHEFgHkQH4X3PF4i/WFMh2CfoM7icpJhzKB0V4E=", "hHddZ+KbqyohgFzcwrImcs+MoJQTbwU27pqiUUeC7PM=", "AJuhXH64c23Mm44OjT2GZeTfsjFOziquljqh1CMzJwU=", "rbShoogEYsvtTTRjVG+A3d5VC5LjKJ77OloZ6fYItVg=", "nDSqgiQhxEQfEq3JxvSSVIqfvxCtr91K86j/ryJ9XOM=", "FsTmRIbptzAz0D25ynRBiOR9analX191HSo9YIeuciI=", "ly7ae3cOgg/ZEiDKwwEuEOHIV9p8vSXlsgBu3YB/DTo=", "aeG1a0HG+l1eCY0Wkm8SUDOyMqEtVPjS3RPO2ysRMbA=", "Zi/deTlSIcuAHRAkyCrI8CskaV5msR3+qkvuFCHGdzo=", "/UR1o47gWTWoYegPDVfuX6HyBkQ6ZQGiFBv9IKG/cZo=", "RDbueUrwmWUiv7Zn7C8V+hdahtOfAu4wVpO+fMhJlXU=", "bakAZvrxSwe+ytZVwX+176dchMMH4D7MrD9p3jqXCBw=", "whDqNoYr/CiV1TckGqCWAeugLLF2g7GsQhB2GiKmU1g=", "DSbbdJeatMfpWz9G+oXHYcdSOeVzKnyA5aW77b+/crs=", "hlXoCvMGx92ILiBkXTGnDeQSxLmMATg0Obm40R6c8Mw=", "6uoEwbTN2FYc13zYb/m9bFxxkv6QqeKGQ/s3WR+DTos=", "lmF2BzXydTAN7yL9Gz8Snznz3e207+jqpL2Fgiu/06Q=", "mEN69I5D1edxT8k5XLvuQDGy22sdGtJjEZ9cNNVW4co=", "ZOBttqLsnGL5CvzFWaWvX/Yfi+UgklKYcPIrSOgZnh0=", "mxMWByJSPQLwiHsAbsqb/4OaSraqmt7RZ2MLDoGmsrY=", "ay7oaPs9N+LiqzxNpVjDXfeno87SVNztk4zA2kWz86c=", "uv0FnZ3+YgCtZU+2ZsPolpl3MmiJ5nav+rpxgRZZ3dw=", "/UojjhDNMxWY7ie7cq7hL8Fqxht+llE1WwEGtdrx/rE=", "zA2gk1S9ZkmZIS6HCYTBU5RdjqtPNb4nEoWBSKzffsY=", "uOXjZ8Vgd0E7C4CtIk9PX6rpBOjBkuwN7EAPERf7jSc=", "XnpJXcMbeIt133Y4L/q9ksmHdS31aoJr3zxT+ER1zok=", "/RVcamw4LqJbLQc7baZNTEUPCY4HLtvMQmNr3Pgp6vo=", "9MzZgjnGgBx6BAzRguU3/g0zHwxdPNg/WC2Wv7fX7YQ=", "p4768g8n2hZd1gVqVk6qsXQht8NJGuWG4tC7/X3fyiE=", "NLPbuUdBJhdY3OedbbjS/IPouk73Y58b24EU+T4jcPA=", "z0xImnSqiOO4DL2Ga1D0LaYm08wuDHFy+BtFtJ80jPc=", "VNFXeQ9kHwM29lw8F5D5wc8I2WjdUQy+OQA6UzW4IV8=", "HGsYDUsPPUO8HorzIV22hgzywuKwQ0QFRk88I/5HWoY=", "pTr1TofLe8SVldsvSOg6uyn8XsOfCwHc2Hhdqb6e7lI=", "9SJxl6PWv7TEmOC8ZoS4C6cApE+/QhMygV2e5BjxG9I=", "vZaiw4JaM5sV+d3xI9aEl9uYaI3lUVO3jpka4xNZoWg=", "qllq5TLiTG6p4580bvHacIHfJf5hKLi3+BrsFEW7y8s=", "zhDTzsfX1IWFQa2wmryswRfoC5XI0x9gO+K0/6+fx0k=", "iSV76sWeRvWIt9dURDpd4wOITNzEpo2MKzOcY3H7yWM=", "V2XhI6/J+gSdm+sknwnkne1Wwsr5VqEJyQWyVcUkX44=", "/yLo89pB6WeMhYuIU3JrS6DGnkmus7Iz9tPQh8pZdbU=", "cG53eJg3uBF09FdENPl53jUnCR7/+43zX3uplh92U10=", "AEyp2iTRNmi+1geoULf36y/evm/0rpzDF5/lfBDQkbU=", "Zd53Tmpn2hr+w/eoLBK88R7najgRY+cutOLhVq30+Vg=", "JpQEd0RQr4veE86KehHxefuNoyKcw8oP/v9g69Ui3bk=", "6exHuQnL5XSa88EQDLZiYzy/+WsqDSanKGSbFwn2ATg=", "JoE84dKLEaxeqydpiEW2fZ3omAH71yk59qF5mGIQvks=", "1gsk6BaAdUT+ZiUzElrVLJtPl6LkxZ+A6SCuNMGD4e0=", "nKXv7GGjhS6OgGvRlA9vKep/vQtKbIcaXU+8ZHpgxRw=", "/DqC6bXZub3N77BoydmPz+Rq1XNfPOH9DMihHyKQmeg=", "gHZzW2eMoIp4LH2ih2SlJZCf6XNaX+ou19l01IJaEjg=", "+AcQ0ShR50h79oCAqB0gv2vEQ4HBywY6gj1CPbmHJdo=", "6izNsDd1KubzGFZQ/yPuNl2NDKHhSEz5vvbj03Gg9+w=", "QcHIn4NbctaiMo3BOxadLMQcn+ViUfwqhdkbRMAMtZQ=", "v/mTmdl4qOM5qRB4Ab7J9GVepuC6gsL5Qlcqf8Y+97U=", "Xo651cjNKUMHWnnmLzPbZNGbpjdeAgrLs/96gg9RbXg=", "pP6iuquF0UX/Q56FdQXLMZKEtFWD+ui9YxWvQHzAONE=", "7eMnl52YdrMn0h6HRneRi1WqoClE9TCxavyEQju9R3I=", "5VysI/ZZZi6Z2hC5SKVEPtlBlu2MBCQ9Nhu/IbnTr6E=", "eBrVxwjqyaSbFQE/hR4FUC8Fc1EesSsxOXgtxKQhP0E=", "hqq55Fw/+h8dedIvH6xUSLwzohCe1xOzrnk8NSKkocg=", "r4YWyHaGYLrtc/nksr/luFUO3Ur9vM5jKtu7rZCrAFw=", "jqNjcoHL6T5uN82MN7dogG9k5LEAYuJq4DZWbJPmYLg=", "PnkUzOT1rMZ72J81Z7HCxGWJCgu1tx1gZPVvcm7swjo=", "qBz9GPcEC53+/sfW9At9TWJNA/osDJvL87cUFnqodvs=", "NDPgAw5fen2HjDCN4ZNiHFowCZw/Ufik5AyedB7emDw=", "8AIofCNSstuGTwLkVH4XHdS32sNArH4V3Ecu4f1XHDk=", "vY7pt7pVjhqy5ZOPl8LP0w38AjtDRMWsIQ6lR+2XjPA=", "G92YpfjwhbPHdlr/uzNaHc6GXEJvnQP/he36pCeEScc=", "xxkVnVTkse9DmTvBhk0/J1Oun5eD6nZqrVDq2r4ZJIc=", "3xg8ui4Pl9qPiy8QvkWtL9VFvOeObECnhUPNXH4uZx0=", "pxniITPydBhWGg2A7wzErrbGBu0i2usnZDdYi3CUZLM=", "l3YrUJu7cyiha/qJ5Cw1jui/LuTbVYJlWSkh6CwRbjo=", "nyXsBZpZj2mkYdZj7kOyJLT78vbyM75LGCmpZ/s84+A=", "Kl5ooxtktDLrEyDVAF2AiG2cmZLKVLtxjAnFFaKSdAM=", "+SJZ5JNSKA35paE5NnrPxs1qA6uL/my855QBqNAbnqw=", "P7qOQ5W6lE9fd6X2MJaCSCVr2TtblNwirA99bYjKZX8=", "Jcs7NqZI7bci07Jmw5P4haDitSGRdkey486W8GqEu2M=", "XXUDAmxETPLQMK5W+S8dGnHwSPKUTlcqtHn0cNeXU1M=", "iYNLRidQebQZ+pHsYwMjQcoxA7y550gZjUpK6iW5Tuc=", "l1ggQCGAmxyRbAVXfX4M171hzqa6DQZ3QwYwvfHzw7A=", "K1kqHLOBjqX9kP7aChmcYZ/3W/NdsjNUr0UCaM3or8M=", "r2SkJqKNioq8wPVAyO8wLjPwVOmDOUv3fic2/OH30m0=", "wqCZkwfaLtSSJsDAMke8BQ8IR3xx/Utni3byZzdhG6w=", "fZE6xHJxsZOYoh/oKw1s2BAcjYm4V0JKc3/3WPSYVjw=", "jcjCdkLEsQwTP2P1o1nWUyLpCvLoYoBy3JKDWWML4Gs=", "DlP3vJ3vEDfLEGV3Pc27tIkvMHOtQnvbGSZYyAMFFNc=", "3TedErIq/hU228+W0ZYHou9XJDzoE1UtCGZI+zOSSQ0=", "W7r0V6rfXaZGDw7iGgJ6a4fyxdlHLCfLCct331Ct76w=", "G/M5j9y0pEebjvbHWiXurmr9ZZVgSPExw0oN2L1dqq4=", "0LdnQVEOCDHZ/NF8Dslg79i5+Rgu54ospgJDaPcdZTI=", "ObQgLEjDCrAJS1D4UYfdmU/5jvo3P9CfPa7v7n6GpI0=", "FcdibpBDQpbv6wf+aAZNRLGg3aEU4klQkrLypnWtONo=", "74uof9KtjlDvCgJtdXxv8tJfv7K1B3DZEY7U6S+XuXY=", "yf0pFGtq6rYSdiF2RoSJaEyu0thcr6hgEq9D9QanOZI=", "HuhLzaV+f4sPQS2NtWvk7JaNIdWlMcn37XG6E6jTgHs=", "dKtqcgrxRDHCwuWFe2Pc+KULNznDBOdAdoMfD5GKCno=", "SIJPlvIzFtmsf5345iqWmTDoxTux7IypeXm+TQ3Ytls=", "HoU4tN/PUAyKAfxsikodpsaNElcAik+cDy8W/bcnnPs=", "iTSS3W7F8a+4+f/3Ge1QORMUbDtefiMLCmCzRN3Ynns=", "Lysitf+v4JC3TIfRoYrWZoOIWvGcNWBD+tHlvyONbmE=", "o9oT8jXfhK7uAfti3nSCeuTzXc6QxjexOSohY5krggo=", "hW2RQvROVCc4go1PpigIerb6TBOmii+K9j/ueb4jvFs=", "lnmyNF9sCMNfnIBd9IHtGcBd1EV510I07ofmuGDf0wE=", "aFIozNbFa0ta1K79CG2SX/slJJHBpaLIMXwvvSeU2Qo=", "SkmYYwHLA4WcsU93VmxG1cf/sLLt4nUPJCUJmUni+4Q=", "1iNm6Dhd83Slmkysd7UQUlJD4ihjoVTnpyEpeie5fgg=", "gKsAntrFGPztdrTFJUmnI51rke/fv1pxnh1rlo0fZxU=", "grmorVQ5lX0zf5Q1vgkPzWnyjxRzgZtbF0s4KJPNh/k=", "pt+2Ol43n2THLoXga8anW6OWli1R/nXfY2KpYXb0PLY=", "sM4SrNYjDRsfqwz1r2EWhUoj0F2md+LQY6TOgMb8y+4=", "6D6Ik6udzYMlqSobfuPG86+ciLWcN99nSd2RtzHlc48=", "YUPCQUc21WmAcqts/OAnhLcO5aWiwgFS8zFLDaP0TMk=", "kW1ItquVna6Ra9A5d8RYaOZAqh2wWhrC6BQoE9ACHUI=", "BVIHxRDPfonLYKEppyh/re54o3d61FceorY9t0meXwI=", "P3M+oC1U4dqXclt8iG5LaSwHY5yuAYdP7TCcx0HQouI=", "yU6nBo9yxDi7L5wA8yFWlfHe+v+LCX89e20ieRcbLkw=", "pvSqukdMWb5YJGZqLDiMc2r/xEUg4YXbkIh80UMEj5k=", "9Dpq8e8Q+rjiZUSxkBG0+cNdu+XgUNAMvpLyuLim4Ew=", "IYRNRAnJwokRinHrecqJRYU6q4tbTAP85HqNYSIHgqk=", "Bjf5+3Vilx7QvmQei34c6tffWER6Faiw7hS4E63Dilw=", "39Y1JZMIFs7NBYg0pJnR1c6+DLvGaD7o7MI9wCDis3s=", "PQv09QYH5bIshhSfwgfq7yOXntq/h75vgt9rFsweWwI=", "hiEno9Th3wV15/q+J1k9RavsS68xIZ3glIhGLlCrWoM=", "W0suwABMI5/+7ULtnct3KlCVLf5unp7TpjB/ey7g5hs=", "3BIG45XpdM34Y2o4VIrVfEQB2JkNIMURYohBR9ieH+A=", "wkU5cX6YFHhucOXcsy/y/dXiHYksW2Fo5Ev92COX65M=", "PCiGFnOZfOvKk8zAuOAWuC8UDqUsHnVKaMe4MZNZHKs=", "nwOzskGvG/PCO3Dh3pzlqVKtNrUCcNUdRYPsP6H+YPk=", "5tajTQiMNzGatxliANDultJlSDO//AJN9lx6ynbMyUU=", "CT59l8+rqtSOZGZW6p9tR4NxAg+EDQ37duoWkprzoIk=", "QcYGltwK+2mKtIavZO2TxhSznJa495GktyW1NegxxG8=", "l58zlVzO91oDVkPpuCFbzUqCABvNinQbqbOMQKpdYmI=", "HnqUVPvkizZaPbf7QH+WmsQ8gO7abFQZjIcF3CBE7sc=", "Zt75o3BdguBcrz+JaAdocuJ2j0oHFftPAK63v4xFhTw=", "lbTn6VO3hv9gkoIrJFR+oY63YZ0+Y/rkDFpIQ8gHaHw=", "NcA9Exf5NOPCN2yemOESibVBHialpsLUhF22MHVkkVg=", "ffpB5LEcIbVuTH0Ol7GORuiB8fF2K9g6b6prj6IIigk=", "yliE7Lpii9pszjk1bopRzjXR0RP/JGw2SgEX2kXoR8s="], "CachedAssets": {"VpG6RR+sGvgiG0+kRhhg3LHt7oVcu+vWUgl74iATE+s=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\css\\admin.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "css/admin#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "a5qpn6nwqe", "Integrity": "YvCbOf9qEAOfXt2Bj2DkpVbwPfcAI8+sXrzUCDaJ9wY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\admin.css", "FileLength": 10555, "LastWriteTime": "2025-06-17T14:00:43.9387761+00:00"}, "Y43vTQmguR1HwfWmBThhj0laYDOF4zztgf4rar7BvqQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\css\\color-fixes.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "css/color-fixes#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "avs8i5688v", "Integrity": "2tV2jS+o19m1SbUqyHwH11jlJwv36BwPT8l5XuKR6AY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\color-fixes.css", "FileLength": 9464, "LastWriteTime": "2025-06-12T03:27:05.5565859+00:00"}, "QTwtXdWxEc1SdW/Es/sYPP3oPSgLQaReOPJ77Ly/tRA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\css\\page-specific-fixes.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "css/page-specific-fixes#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s6so21365t", "Integrity": "tDpLQbwrv+9m63a6Yf8ym5DHvNDHPNYK5wZ24P4w4hA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\page-specific-fixes.css", "FileLength": 9930, "LastWriteTime": "2025-06-12T03:28:05.6420101+00:00"}, "QfIwLWcnRYuGHOIvSkSg1Wb7qOFV8+PyzfEaODTa9Is=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\css\\site.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x3u8v4r6s1", "Integrity": "bYlH71aSsfpd0Ikxr8yTBvambNffcRJrKAuQ0Yo741c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 14625, "LastWriteTime": "2025-06-12T03:28:55.6781086+00:00"}, "CA5ohl5cSHV/YlScu6aWvJgsrIwv0JyA9ZtLqzAPCnI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\images\\BusBanner.jpg", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "images/BusBanner#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wswepo8j2e", "Integrity": "cAnkDCCa7L/Aave23u03hBPCxIRe4f6RE3To2FKuZHk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\BusBanner.jpg", "FileLength": 412962, "LastWriteTime": "2025-05-29T16:53:47.7035087+00:00"}, "MMKphBGZ00ip1cX6JVta60lUHa1dd50dTaKmUHOi128=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\images\\mancity.webp", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "images/mancity#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cuj6g20mhu", "Integrity": "DH5JH9mMrWeeWzwMPmw/ViJKk9xKSC+vp0gum9lIKVk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\mancity.webp", "FileLength": 518686, "LastWriteTime": "2025-05-30T18:28:16.8728415+00:00"}, "1l+q4+tgl+3P3iEfii9wjmI69NnzjR9gqt4pqyy0foQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\images\\mancitybus.jpg", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "images/mancitybus#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "p3yf6yxrqx", "Integrity": "m0cRtQDSNpPR+ACUJaDKBr9p/pVGycQPH9X1M+O0UaA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\mancitybus.jpg", "FileLength": 162920, "LastWriteTime": "2025-05-30T18:33:06.2146025+00:00"}, "Ui0bIWRsFQRARk0DqAeeISZwYkz+GtxT8T50KZbB3mQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\images\\readmarid.jpg", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "images/readmarid#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qmrga2uz31", "Integrity": "Ht4gl3yj+neQ8mnCCGrl45M8AcNj7jKzkDyjcCswUQ8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\readmarid.jpg", "FileLength": 114373, "LastWriteTime": "2025-06-06T04:00:01.2177346+00:00"}, "hkAMMfs4YrdvsY4Zk1Ze1sK4VE/V+t+5ogrEVF22Aq0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\images\\realvscity.jpg", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "images/realvscity#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1ydqed4cvh", "Integrity": "59RxYW9+ZsTMhwpGhZpIcmr4H7a5JMUifRytRo4jABI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\realvscity.jpg", "FileLength": 56258, "LastWriteTime": "2025-06-12T03:17:05.6266269+00:00"}, "gjyUN1J6R3ADM8wd7Bc6u1GFS6HxJdMds6202HB3Ogk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\js\\admin.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "js/admin#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "etsnhtcic7", "Integrity": "XolJhSBU5njpOhuk2k1Crn3aOrQLTyqIodcN8riL0oM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\admin.js", "FileLength": 13289, "LastWriteTime": "2025-06-16T18:19:07.291451+00:00"}, "y+gzeJcgDksGc5XokVH4sZSxhu2jQAIkIcj2x9Lw0yg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\js\\color-contrast-checker.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "js/color-contrast-checker#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9qy6rw296h", "Integrity": "UdCV8Ngi9MF71ly/M6Aiho7ctndl/MHgXH9ekj7SUeQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\color-contrast-checker.js", "FileLength": 11054, "LastWriteTime": "2025-06-12T03:29:44.1086413+00:00"}, "89qWj7NWQtq3H7Xlim/s9MT8usOSrUuQ4XNkzKMU3Lk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\js\\site.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-05-29T16:25:30.0858949+00:00"}, "KBeIup7NZoGVAf6T2WeT4ktE7r2Ee3E5qEZg8c8rHTk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\js\\user-management.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "js/user-management#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z797e9e25e", "Integrity": "bxyY72CMqX09sfC+tW33vX61fEqryz0r8YPyUmO63Ao=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\user-management.js", "FileLength": 7153, "LastWriteTime": "2025-06-15T13:31:56.5626835+00:00"}, "MgzX187z1VLS/mxCVyImLqGnZD/TGMa/MiAgMDK7SKM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-05-29T16:25:30.1478105+00:00"}, "+9FiNR2LA+JkfYRV8ZmAsfU3ZKMpnCl2m4zrQuazG64=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-05-29T16:25:30.1502121+00:00"}, "YMvfR1mb73hf1tVkaUb6KMV4OtpIKH4kkzYaGuj1RHo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-05-29T16:25:30.1502121+00:00"}, "sV2tzNTaBYc5wLHykioQt+1oH1vPTm7qPJ+uZ1klSis=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-05-29T16:25:30.1502121+00:00"}, "+KqglmyNEputqLd3L1J195VlOv7DcmJvdjyX9SflybE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-05-29T16:25:30.1502121+00:00"}, "I90+1tOxwORrE2lVsmuTVALRWoRsZeGU9YOcGaKDviw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-05-29T16:25:30.1549208+00:00"}, "o4lS39EKK/8+4VfqnLxI5lByKFHP3UYK/xJYrc36fuM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-05-29T16:25:30.1617274+00:00"}, "nx1OtnP+ryN4Ek2IreZ8DVVuMMizSS4IzrbsDIGSy0o=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-05-29T16:25:30.1617274+00:00"}, "gnq2cLzf7cRS/AwxClZ5kg4w8kBghpRi3K5WNUCpcWY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-05-29T16:25:30.1617274+00:00"}, "ib4p/+ug1o5sTvRDO/HzRwp5ByUFUS7vlm0LNQxN/hU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-05-29T16:25:30.1617274+00:00"}, "9wrCectbVHkIl4Lgkgp7rVeQBwF5EJ/aLB6vTaaceBA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-05-29T16:25:30.1662367+00:00"}, "t9ZjYiWa6JGtmRVepu/3f6IOfX1uxaTwSCkkpreo5X0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-05-29T16:25:30.1662367+00:00"}, "efV24TdfKFTtyEE8NDmE9ok/OYHcLSGp2BlQfctC3Rg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-05-29T16:25:30.1662367+00:00"}, "BBUaI5XSk7xmMvJ7D2HwMMB5WTSh1JCDfUDfkxJlNnY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-05-29T16:25:30.1662367+00:00"}, "VyBWR+TtxknGk+IFAT9cDaVwWZ0ppOBSv2ZBwYWBaik=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-05-29T16:25:30.1662367+00:00"}, "mkBzPkF6EH0WD+7gi2yQZcy9OqVuViCvgcHrsHE9TWY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-05-29T16:25:30.1707744+00:00"}, "2B9nbnM6uiIxDWI0yJJ0dIajVe+XOiMzLGMc+FzSOTI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-05-29T16:25:30.1707744+00:00"}, "Pk5XDsnw2xaA9fExKY+wAISrPS9nVrGHxRe+3FzuN7M=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-05-29T16:25:30.1752808+00:00"}, "CN0DTUjOh3yiF7kschJN+mdW9fKjItQjWJwA0CPEkzA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-05-29T16:25:30.1752808+00:00"}, "USyi7wZIHuUlgdq53WPCLHJc6Yhm5bGv1CFPlcbceO4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-05-29T16:25:30.1769657+00:00"}, "cAYvqfVuLfykfBx3IVzioJZ7jXJ4+arFxDueq8z64QU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-05-29T16:25:30.1769657+00:00"}, "OZFyjoNCTUYWToVaSVMT4y+Rej8F+2OrdopRZl8syN8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-05-29T16:25:30.1809721+00:00"}, "gfisdeWrEAbsvOUbqblCrF4CANRN2oiO6TVKzy87FJk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-05-29T16:25:30.1809721+00:00"}, "k77gHl7GxJyWrGsHV9RA0xUhrS10ENDWGcxEOXtSNxw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-05-29T16:25:30.1809721+00:00"}, "JQFzeZVPMJbSWJc+343QOI3bM4Veuc7DuVOqABxT1Tk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-05-29T16:25:30.1809721+00:00"}, "ibt5UaOvNIHqDZkWaJ8TQOZfVmbeyxfuTX2aozc01cI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-05-29T16:25:30.1871172+00:00"}, "nnKSaTXJhZ5ZFfNeOUTvBiBklSxSU5U5grEGMudb08I=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-05-29T16:25:30.1871172+00:00"}, "dQKlBmu5b79wLU5Vzd0yVbe+UaMfi0+w8yNFfSCsYvE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-05-29T16:25:30.1911245+00:00"}, "dBNId30ehqLvo+ls6xlvXEsJr/1Cx/YJ0r7XHVgcSIY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-05-29T16:25:30.1963316+00:00"}, "6cs72PyUDRnUL468tapRqmBbU/D+bKqhZzWuopZ3lUI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-05-29T16:25:30.2009634+00:00"}, "xYnhQ7ff5ivYR3vSNxtiXTnMI+hGBM9zNoppnpQyJrw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-05-29T16:25:30.2014891+00:00"}, "XNkuZ+F+r3OIloeF9g5uIGCOqfm3JJZpudcRBgLN8lc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-05-29T16:25:30.2077248+00:00"}, "hDm8crFxOx7iLbY3wt0y9CsTM/C3sDexH9Ijx5IC1t4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-05-29T16:25:30.209978+00:00"}, "VQtCbFEQmI4ew8P/VkX96NMlOdqthGFQEBNHdlptDnY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-05-29T16:25:30.2160665+00:00"}, "30goXUtPmCesDGG+rYo6o025UTIXENQBoJgI81KkeR4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-05-29T16:25:30.2165938+00:00"}, "auWJKhmtHzWg2iGzseZzO/Acz2OIXevr7wQnYeXISZM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-05-29T16:25:30.218908+00:00"}, "CWdTQHWROlTgPC6fz6dax8vVIaHTL/So3GpvCPslJ3o=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-05-29T16:25:30.218908+00:00"}, "JTUdtBo36Hg7vBf3C3G70VxptumCxsRx0fQ2c5gcE9A=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-05-29T16:25:30.2210954+00:00"}, "1eVHByR4MFjlq4xjkNHvphoGS57cDQ7HlRFXQSVpQ/Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-05-29T16:25:30.2216845+00:00"}, "C3BVvGfF0GRnZ/BSQejVI+PQnai0Xtr1zB7aFzE78lQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-05-29T16:25:30.2614085+00:00"}, "1ekKo24BxjOqsYN27jlfCbYxpqD63RpC6RNq37911rg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-05-29T16:25:30.2614085+00:00"}, "Bt7f8zJNFpI29lcu2SSs8AXtq0ZeCbPK1iZgQZSbNSo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-05-29T16:25:30.2659992+00:00"}, "iCI8stvzQti2bnLC3WwhG5cn68QD0slw2jOUNF82n7Y=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-05-29T16:25:30.3060225+00:00"}, "75BC1UKp3NCXutLEidMNgLD+rqobELklAje/bypdp2E=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-05-29T16:25:30.3151027+00:00"}, "d3+fQdRgDAGnBaLL2ipbs+py8GlN7tRrugn4kyDeMnw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-05-29T16:25:30.3009598+00:00"}, "9/Ky1iY1mycg/0dq5vqoxtQzNeQrBnNKoVn6P3K59e0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-05-29T16:25:30.3377384+00:00"}, "ztOjQQpLZ2ee60e7sVwJ2fN3raRTKReQQenlOTaExBA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-05-29T16:25:30.3458131+00:00"}, "jHLDHuaOK/YZNBq9/DMDAnzZPiXu9OOBoSO4vddy/V8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-05-29T16:25:30.3458131+00:00"}, "+QC6P+K8cYhUAdKaP+R04FX5vSdlL7ncApUYYCRNOGQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-05-29T16:25:30.1398631+00:00"}, "wA61BHEFgHkQH4X3PF4i/WFMh2CfoM7icpJhzKB0V4E=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-05-29T16:25:30.1433851+00:00"}, "hHddZ+KbqyohgFzcwrImcs+MoJQTbwU27pqiUUeC7PM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-05-29T16:25:30.1448272+00:00"}, "AJuhXH64c23Mm44OjT2GZeTfsjFOziquljqh1CMzJwU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-05-29T16:25:30.1458397+00:00"}, "rbShoogEYsvtTTRjVG+A3d5VC5LjKJ77OloZ6fYItVg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-05-29T16:25:30.3166531+00:00"}, "nDSqgiQhxEQfEq3JxvSSVIqfvxCtr91K86j/ryJ9XOM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-05-29T16:25:30.136091+00:00"}, "FsTmRIbptzAz0D25ynRBiOR9analX191HSo9YIeuciI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-05-29T16:25:30.136091+00:00"}, "ly7ae3cOgg/ZEiDKwwEuEOHIV9p8vSXlsgBu3YB/DTo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-05-29T16:25:30.1398631+00:00"}, "aeG1a0HG+l1eCY0Wkm8SUDOyMqEtVPjS3RPO2ysRMbA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-05-29T16:25:30.3112225+00:00"}, "Zi/deTlSIcuAHRAkyCrI8CskaV5msR3+qkvuFCHGdzo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\test-routes.html", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe1\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "test-routes#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nne9mve8jp", "Integrity": "c0TA3uf1rDYask4SWDqDgDceep+WGXscRu6tdkX/xrk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test-routes.html", "FileLength": 440, "LastWriteTime": "2025-06-15T13:31:56.6294491+00:00"}}, "CachedCopyCandidates": {}}