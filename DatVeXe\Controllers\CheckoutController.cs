using Microsoft.AspNetCore.Mvc;
using DatVeXe.Models;
using DatVeXe.Services;
using System.Text.Json;

namespace DatVeXe.Controllers
{
    [Route("[controller]")]
    public class CheckoutController : Controller
    {
        private readonly ILogger<CheckoutController> _logger;
        private readonly IMoMoHelper _moMoHelper;
        private readonly IPaymentService _paymentService;

        public CheckoutController(ILogger<CheckoutController> logger, IMoMoHelper moMoHelper, IPaymentService paymentService)
        {
            _logger = logger;
            _moMoHelper = moMoHelper;
            _paymentService = paymentService;
        }

        [HttpGet("PaymentCallBack")]
        public async Task<IActionResult> PaymentCallBack()
        {
            try
            {
                _logger.LogInformation("MoMo PaymentCallBack received");

                // Lấy parameters từ query string
                var parameters = Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());
                _logger.LogInformation($"PaymentCallBack parameters: {string.Join(", ", parameters.Select(p => $"{p.Key}={p.Value}"))}");

                var orderId = parameters.GetValueOrDefault("orderId");
                var resultCode = parameters.GetValueOrDefault("resultCode");

                if (string.IsNullOrEmpty(orderId))
                {
                    _logger.LogWarning("Missing orderId in PaymentCallBack");
                    return RedirectToAction("PaymentFailure", "Booking", new { 
                        errorMessage = "Thiếu thông tin đơn hàng",
                        errorCode = "MISSING_ORDER_ID"
                    });
                }

                // Xử lý callback thông qua PaymentService
                var paymentResult = await _paymentService.ProcessPaymentCallbackAsync(orderId, parameters);

                if (paymentResult.Success)
                {
                    _logger.LogInformation($"Payment successful for order: {orderId}");
                    return RedirectToAction("PaymentSuccess", "Booking", new { 
                        sessionId = orderId,
                        transactionId = paymentResult.TransactionId
                    });
                }
                else
                {
                    _logger.LogWarning($"Payment failed for order: {orderId}, Error: {paymentResult.Message}");
                    return RedirectToAction("PaymentFailure", "Booking", new { 
                        sessionId = orderId,
                        errorMessage = paymentResult.Message,
                        errorCode = paymentResult.ErrorCode,
                        transactionId = paymentResult.TransactionId
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing MoMo PaymentCallBack");
                return RedirectToAction("PaymentFailure", "Booking", new { 
                    errorMessage = "Có lỗi hệ thống khi xử lý thanh toán",
                    errorCode = "SYSTEM_ERROR"
                });
            }
        }

        [HttpPost("MomoNotify")]
        public async Task<IActionResult> MomoNotify([FromBody] MoMoIpnRequest ipnRequest)
        {
            try
            {
                _logger.LogInformation($"MoMo IPN received: {JsonSerializer.Serialize(ipnRequest)}");

                // Validate signature
                var rawSignature = $"accessKey={Request.Headers["accessKey"]}" +
                                 $"&amount={ipnRequest.amount}" +
                                 $"&extraData={ipnRequest.extraData}" +
                                 $"&message={ipnRequest.message}" +
                                 $"&orderId={ipnRequest.orderId}" +
                                 $"&orderInfo={ipnRequest.orderInfo}" +
                                 $"&orderType={ipnRequest.orderType}" +
                                 $"&partnerCode={ipnRequest.partnerCode}" +
                                 $"&payType={ipnRequest.payType}" +
                                 $"&requestId={ipnRequest.requestId}" +
                                 $"&responseTime={ipnRequest.responseTime}" +
                                 $"&resultCode={ipnRequest.resultCode}" +
                                 $"&transId={ipnRequest.transId}";

                var isValidSignature = _moMoHelper.ValidateSignature(ipnRequest.signature, rawSignature);

                if (!isValidSignature)
                {
                    _logger.LogWarning($"Invalid signature in MoMo IPN for order: {ipnRequest.orderId}");
                    return BadRequest(new MoMoIpnResponse
                    {
                        partnerCode = ipnRequest.partnerCode,
                        requestId = ipnRequest.requestId,
                        orderId = ipnRequest.orderId,
                        resultCode = 97, // Invalid signature
                        message = "Invalid signature",
                        responseTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                    });
                }

                // Process the payment notification
                var parameters = new Dictionary<string, string>
                {
                    ["partnerCode"] = ipnRequest.partnerCode,
                    ["orderId"] = ipnRequest.orderId,
                    ["requestId"] = ipnRequest.requestId,
                    ["amount"] = ipnRequest.amount.ToString(),
                    ["orderInfo"] = ipnRequest.orderInfo,
                    ["orderType"] = ipnRequest.orderType,
                    ["transId"] = ipnRequest.transId.ToString(),
                    ["resultCode"] = ipnRequest.resultCode.ToString(),
                    ["message"] = ipnRequest.message,
                    ["payType"] = ipnRequest.payType,
                    ["responseTime"] = ipnRequest.responseTime.ToString(),
                    ["extraData"] = ipnRequest.extraData,
                    ["signature"] = ipnRequest.signature
                };

                var paymentResult = await _paymentService.ProcessPaymentCallbackAsync(ipnRequest.orderId, parameters);

                // Return response to MoMo
                var response = new MoMoIpnResponse
                {
                    partnerCode = ipnRequest.partnerCode,
                    requestId = ipnRequest.requestId,
                    orderId = ipnRequest.orderId,
                    resultCode = paymentResult.Success ? 0 : 99,
                    message = paymentResult.Success ? "Success" : "Failed",
                    responseTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                };

                _logger.LogInformation($"MoMo IPN response: {JsonSerializer.Serialize(response)}");
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing MoMo IPN");
                return StatusCode(500, new MoMoIpnResponse
                {
                    partnerCode = ipnRequest?.partnerCode ?? "",
                    requestId = ipnRequest?.requestId ?? "",
                    orderId = ipnRequest?.orderId ?? "",
                    resultCode = 99,
                    message = "Internal server error",
                    responseTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                });
            }
        }
    }
}
